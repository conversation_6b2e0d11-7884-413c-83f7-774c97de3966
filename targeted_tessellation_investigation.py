#!/usr/bin/env python3
"""
Targeted investigation of specific NYC tracts for tessellation population extraction
"""

import json
import pandas as pd
import os

def find_specific_nyc_tract_examples():
    """
    Find specific NYC tract examples by sampling the tessellation file
    """
    print("🔍 TARGETED NYC TRACT INVESTIGATION")
    print("=" * 50)
    
    tessellation_file = 'population files/tessellation_with_population.geojson'
    
    if not os.path.exists(tessellation_file):
        print(f"❌ Tessellation file not found")
        return []
    
    print(f"📊 Sampling tessellation data for NYC tracts...")
    
    try:
        nyc_tract_examples = []
        
        with open(tessellation_file, 'r') as f:
            # Read line by line to avoid loading entire file
            line_count = 0
            feature_count = 0
            
            for line in f:
                line_count += 1
                
                # Look for NYC tract patterns in the line
                if '"36' in line and 'population' in line:
                    try:
                        # Try to extract tract info from the line
                        if '"tile_ID"' in line and '"population"' in line:
                            # This looks like a feature with NYC data
                            feature_count += 1
                            
                            # Extract tile_ID and population using simple string parsing
                            tile_id_start = line.find('"tile_ID":') + 10
                            tile_id_end = line.find(',', tile_id_start)
                            if tile_id_end == -1:
                                tile_id_end = line.find('}', tile_id_start)
                            
                            tile_id_str = line[tile_id_start:tile_id_end].strip().strip('"')
                            
                            pop_start = line.find('"population":') + 13
                            pop_end = line.find(',', pop_start)
                            if pop_end == -1:
                                pop_end = line.find('}', pop_start)
                            
                            pop_str = line[pop_start:pop_end].strip()
                            
                            if tile_id_str and len(tile_id_str) >= 11 and tile_id_str.startswith('36'):
                                tract_id = tile_id_str[:11]
                                
                                try:
                                    population = float(pop_str) if pop_str != 'null' else None
                                    
                                    if population is not None and population > 0:
                                        nyc_tract_examples.append({
                                            'tract_id': tract_id,
                                            'tile_ID': tile_id_str,
                                            'population': population,
                                            'line_number': line_count
                                        })
                                        
                                        print(f"   Found NYC tract: {tract_id}, population: {population}")
                                        
                                        # Stop after finding 20 examples
                                        if len(nyc_tract_examples) >= 20:
                                            break
                                except ValueError:
                                    continue
                    except Exception:
                        continue
                
                # Stop after processing reasonable amount
                if line_count > 100000:
                    break
        
        print(f"📊 Found {len(nyc_tract_examples)} NYC tract examples")
        return nyc_tract_examples
        
    except Exception as e:
        print(f"❌ Error sampling tessellation data: {e}")
        return []

def check_tract_in_output_and_census(tract_examples):
    """
    Check if the found tract examples appear in output and census data
    """
    print(f"\n🔍 CHECKING TRACT EXAMPLES IN OUTPUT AND CENSUS DATA")
    print("=" * 50)
    
    if not tract_examples:
        print("❌ No tract examples to check")
        return []
    
    # Load output file
    output_file = 'monthly_output_files/tract_flows_with_population_2024-month-01.csv'
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    
    results = []
    
    # Load census data
    census_tracts = set()
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_df['tract_str'] = census_df['tract'].apply(lambda x: str(int(float(x))))
        census_tracts = set(census_df['tract_str'])
        print(f"📊 Loaded {len(census_tracts):,} census tracts")
    
    # Load output data
    output_tracts = set()
    if os.path.exists(output_file):
        df = pd.read_csv(output_file)
        source_tracts = set(df['Source'].dropna().astype(str))
        dest_tracts = set(df['Destination'].dropna().astype(str))
        output_tracts = source_tracts.union(dest_tracts)
        print(f"📊 Loaded {len(output_tracts):,} output tracts")
    
    print(f"\n📊 ANALYZING TRACT EXAMPLES:")
    
    for i, tract_example in enumerate(tract_examples):
        tract_id = tract_example['tract_id']
        tessellation_pop = tract_example['population']
        
        in_census = tract_id in census_tracts
        in_output = tract_id in output_tracts
        
        result = {
            'tract_id': tract_id,
            'tessellation_population': tessellation_pop,
            'in_census': in_census,
            'in_output': in_output,
            'tile_ID': tract_example['tile_ID']
        }
        
        print(f"\n   Tract {i+1}: {tract_id}")
        print(f"     Tessellation population: {tessellation_pop}")
        print(f"     In census data: {in_census}")
        print(f"     In output data: {in_output}")
        
        if in_output and not in_census:
            print(f"     🎯 CANDIDATE: Has tessellation pop, in output, NOT in census")
            result['candidate'] = True
        elif in_output and in_census:
            print(f"     ✅ Expected: Census data takes priority")
            result['candidate'] = False
        else:
            print(f"     ❌ Not in output data")
            result['candidate'] = False
        
        results.append(result)
    
    # Find the best candidates
    candidates = [r for r in results if r.get('candidate', False)]
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Total tract examples: {len(results)}")
    print(f"   Candidates (tessellation-only, in output): {len(candidates)}")
    
    if len(candidates) > 0:
        print(f"\n🚨 POTENTIAL TESSELLATION EXTRACTION ISSUES:")
        for candidate in candidates[:3]:
            print(f"     {candidate['tract_id']}: tessellation_pop={candidate['tessellation_population']}")
    else:
        print(f"\n✅ No issues found - all tracts either have census data or aren't in output")
    
    return candidates

def detailed_tract_analysis(tract_id):
    """
    Perform detailed analysis of a specific tract
    """
    print(f"\n" + "=" * 50)
    print(f"🔍 DETAILED ANALYSIS FOR TRACT {tract_id}")
    print("=" * 50)
    
    # Check in output file
    output_file = 'monthly_output_files/tract_flows_with_population_2024-month-01.csv'
    if os.path.exists(output_file):
        df = pd.read_csv(output_file)
        
        # Check as source
        source_rows = df[df['Source'] == tract_id]
        if len(source_rows) > 0:
            sample_row = source_rows.iloc[0]
            print(f"📊 As SOURCE tract:")
            print(f"   Population: {sample_row['Source_Population']}")
            print(f"   Coordinate source: {sample_row['Source_Coordinate_Source']}")
            print(f"   Coordinates: ({sample_row['Source_Latitude']}, {sample_row['Source_Longitude']})")
            
            if pd.isna(sample_row['Source_Population']):
                print(f"   🚨 ISSUE: Population is NULL despite tessellation data available")
            else:
                print(f"   ✅ Population data present")
        
        # Check as destination
        dest_rows = df[df['Destination'] == tract_id]
        if len(dest_rows) > 0:
            sample_row = dest_rows.iloc[0]
            print(f"📊 As DESTINATION tract:")
            print(f"   Population: {sample_row['Destination_Population']}")
            print(f"   Coordinate source: {sample_row['Destination_Coordinate_Source']}")
            print(f"   Coordinates: ({sample_row['Destination_Latitude']}, {sample_row['Destination_Longitude']})")
            
            if pd.isna(sample_row['Destination_Population']):
                print(f"   🚨 ISSUE: Population is NULL despite tessellation data available")
            else:
                print(f"   ✅ Population data present")
        
        if len(source_rows) == 0 and len(dest_rows) == 0:
            print(f"❌ Tract not found in output file")
    
    # Check in census file
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_df['tract_str'] = census_df['tract'].apply(lambda x: str(int(float(x))))
        
        if tract_id in census_df['tract_str'].values:
            census_row = census_df[census_df['tract_str'] == tract_id].iloc[0]
            print(f"📊 CENSUS DATA:")
            print(f"   Population: {census_row['population']}")
            print(f"   Coordinates: ({census_row['latitude']}, {census_row['longitude']})")
            print(f"   🎯 Census data available - explains why tessellation not used")
        else:
            print(f"📊 CENSUS DATA: NOT FOUND")
            print(f"   🎯 Should use tessellation population data")

def simulate_population_lookup(tract_id, tessellation_pop):
    """
    Simulate the get_tract_population function for a specific tract
    """
    print(f"\n🔍 SIMULATING POPULATION LOOKUP FOR {tract_id}")
    print("=" * 50)
    
    # Load census data
    census_population = {}
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        for _, row in census_df.iterrows():
            tract_str = str(int(float(row['tract'])))
            if pd.notna(row['population']):
                census_population[tract_str] = row['population']
    
    # Simulate tessellation population (would be loaded from tessellation file)
    tessellation_population = {tract_id: tessellation_pop}
    
    # Simulate get_tract_population function
    def get_tract_population(tract_id):
        if tract_id in census_population:
            return census_population[tract_id]
        elif tract_id in tessellation_population:
            return tessellation_population[tract_id]
        return None
    
    result = get_tract_population(tract_id)
    
    print(f"📊 POPULATION LOOKUP SIMULATION:")
    print(f"   Tract ID: {tract_id}")
    print(f"   In census_population: {tract_id in census_population}")
    print(f"   In tessellation_population: {tract_id in tessellation_population}")
    print(f"   get_tract_population() result: {result}")
    print(f"   Expected tessellation value: {tessellation_pop}")
    
    if result == tessellation_pop:
        print(f"   ✅ Correct: Tessellation population returned")
    elif result is not None and result != tessellation_pop:
        print(f"   ✅ Correct: Census population returned (higher priority)")
    else:
        print(f"   ❌ Issue: No population returned despite tessellation data")

if __name__ == "__main__":
    # Step 1: Find NYC tract examples from tessellation data
    tract_examples = find_specific_nyc_tract_examples()
    
    if not tract_examples:
        print("❌ No NYC tract examples found")
        exit()
    
    # Step 2: Check which ones are candidates for investigation
    candidates = check_tract_in_output_and_census(tract_examples)
    
    # Step 3: Detailed analysis of candidates
    if candidates:
        print(f"\n" + "=" * 70)
        print("🔍 DETAILED CANDIDATE ANALYSIS")
        print("=" * 70)
        
        for candidate in candidates[:2]:  # Analyze first 2 candidates
            detailed_tract_analysis(candidate['tract_id'])
            simulate_population_lookup(candidate['tract_id'], candidate['tessellation_population'])
    else:
        # If no candidates, analyze a few examples anyway
        print(f"\n" + "=" * 70)
        print("🔍 SAMPLE TRACT ANALYSIS")
        print("=" * 70)
        
        for example in tract_examples[:2]:
            detailed_tract_analysis(example['tract_id'])
            simulate_population_lookup(example['tract_id'], example['population'])
    
    print(f"\n" + "=" * 70)
    print("🎯 TARGETED INVESTIGATION COMPLETE")
    print("=" * 70)
