#!/usr/bin/env python3
"""
Check specific tessellation tracts found in the file to see if they appear in output
"""

import pandas as pd
import os

def check_specific_tracts():
    """
    Check specific NYC tracts that we know have tessellation population data
    """
    print("🔍 CHECKING SPECIFIC TESSELLATION TRACTS")
    print("=" * 50)
    
    # Specific tracts we found in tessellation data with their population values
    tessellation_tracts = {
        '36061021600': 1089.0,  # from tile_ID "360610216003"
        '36061018300': 646.0,   # from tile_ID "360610183002"
        '36061007900': 1153.0,  # from tile_ID "360610079003"
        '36061019900': 1495.0,  # from tile_ID "360610199002"
        '36061017900': 1776.0,  # from tile_ID "360610179004"
        '36061019100': 1419.0,  # from tile_ID "360610191005"
        '36061023400': 2481.0,  # from tile_ID "360610234002"
        '36061018700': 1797.0,  # from tile_ID "360610187005"
        '36061023700': 1962.0,  # from tile_ID "360610237003"
        '36061018300': 1947.0,  # from tile_ID "360610183004" (different from 183002)
        '36061022500': 2444.0,  # from tile_ID "360610225001"
    }
    
    print(f"📊 Checking {len(tessellation_tracts)} specific tessellation tracts:")
    for tract_id, pop in tessellation_tracts.items():
        print(f"   {tract_id}: {pop}")
    
    # Load output file
    output_file = 'monthly_output_files/tract_flows_with_population_2024-month-01.csv'
    
    if not os.path.exists(output_file):
        print(f"❌ Output file not found: {output_file}")
        return
    
    print(f"\n📊 Loading output file: {output_file}")
    df = pd.read_csv(output_file)
    
    # Check each tract
    results = []
    
    for tract_id, expected_pop in tessellation_tracts.items():
        print(f"\n🔍 Checking tract {tract_id} (expected tessellation pop: {expected_pop}):")
        
        # Check as source tract
        source_rows = df[df['Source'] == tract_id]
        if len(source_rows) > 0:
            sample_row = source_rows.iloc[0]
            actual_pop = sample_row['Source_Population']
            coord_source = sample_row['Source_Coordinate_Source']
            
            print(f"   As SOURCE:")
            print(f"     Appears in output: YES ({len(source_rows)} rows)")
            print(f"     Population in output: {actual_pop}")
            print(f"     Coordinate source: {coord_source}")
            
            if pd.isna(actual_pop):
                print(f"     🚨 ISSUE: Population is NULL despite tessellation data!")
                results.append({
                    'tract_id': tract_id,
                    'role': 'source',
                    'expected_pop': expected_pop,
                    'actual_pop': None,
                    'coord_source': coord_source,
                    'issue': True
                })
            elif actual_pop != expected_pop:
                print(f"     ✅ Population present but different (census/gazetteer priority)")
                results.append({
                    'tract_id': tract_id,
                    'role': 'source',
                    'expected_pop': expected_pop,
                    'actual_pop': actual_pop,
                    'coord_source': coord_source,
                    'issue': False
                })
            else:
                print(f"     ✅ Tessellation population correctly used!")
                results.append({
                    'tract_id': tract_id,
                    'role': 'source',
                    'expected_pop': expected_pop,
                    'actual_pop': actual_pop,
                    'coord_source': coord_source,
                    'issue': False
                })
        
        # Check as destination tract
        dest_rows = df[df['Destination'] == tract_id]
        if len(dest_rows) > 0:
            sample_row = dest_rows.iloc[0]
            actual_pop = sample_row['Destination_Population']
            coord_source = sample_row['Destination_Coordinate_Source']
            
            print(f"   As DESTINATION:")
            print(f"     Appears in output: YES ({len(dest_rows)} rows)")
            print(f"     Population in output: {actual_pop}")
            print(f"     Coordinate source: {coord_source}")
            
            if pd.isna(actual_pop):
                print(f"     🚨 ISSUE: Population is NULL despite tessellation data!")
                results.append({
                    'tract_id': tract_id,
                    'role': 'destination',
                    'expected_pop': expected_pop,
                    'actual_pop': None,
                    'coord_source': coord_source,
                    'issue': True
                })
            elif actual_pop != expected_pop:
                print(f"     ✅ Population present but different (census/gazetteer priority)")
                results.append({
                    'tract_id': tract_id,
                    'role': 'destination',
                    'expected_pop': expected_pop,
                    'actual_pop': actual_pop,
                    'coord_source': coord_source,
                    'issue': False
                })
            else:
                print(f"     ✅ Tessellation population correctly used!")
                results.append({
                    'tract_id': tract_id,
                    'role': 'destination',
                    'expected_pop': expected_pop,
                    'actual_pop': actual_pop,
                    'coord_source': coord_source,
                    'issue': False
                })
        
        if len(source_rows) == 0 and len(dest_rows) == 0:
            print(f"   ❌ NOT FOUND in output file")
    
    return results

def analyze_results(results):
    """
    Analyze the results to identify patterns
    """
    print(f"\n" + "=" * 50)
    print("📊 RESULTS ANALYSIS")
    print("=" * 50)
    
    if not results:
        print("❌ No results to analyze")
        return
    
    # Count issues
    issues = [r for r in results if r['issue']]
    no_issues = [r for r in results if not r['issue']]
    
    print(f"📊 Summary:")
    print(f"   Total tract appearances: {len(results)}")
    print(f"   With population issues: {len(issues)}")
    print(f"   Without issues: {len(no_issues)}")
    
    if len(issues) > 0:
        print(f"\n🚨 TRACTS WITH POPULATION ISSUES:")
        for issue in issues:
            print(f"   {issue['tract_id']} ({issue['role']}): coord_source={issue['coord_source']}")
        
        print(f"\n🔍 DETAILED ISSUE ANALYSIS:")
        coord_source_issues = {}
        for issue in issues:
            source = issue['coord_source']
            coord_source_issues[source] = coord_source_issues.get(source, 0) + 1
        
        print(f"   Issues by coordinate source:")
        for source, count in coord_source_issues.items():
            print(f"     {source}: {count} issues")
        
        # Recommend debugging tract
        if issues:
            debug_tract = issues[0]['tract_id']
            print(f"\n🎯 RECOMMENDED DEBUG TRACT: {debug_tract}")
            print(f"   This tract has tessellation population but shows NULL in output")
            print(f"   Use this tract to debug the tessellation extraction pipeline")
    else:
        print(f"\n✅ NO POPULATION ISSUES FOUND")
        print(f"   All tessellation tracts either:")
        print(f"   - Have population from higher-priority sources (census/gazetteer)")
        print(f"   - Use tessellation population correctly")
        print(f"   - Don't appear in the flow data")
    
    # Analyze coordinate sources
    print(f"\n📊 COORDINATE SOURCE DISTRIBUTION:")
    coord_sources = {}
    for result in results:
        source = result['coord_source']
        coord_sources[source] = coord_sources.get(source, 0) + 1
    
    for source, count in coord_sources.items():
        print(f"   {source}: {count} appearances")

def check_census_coverage():
    """
    Check if the tessellation tracts have census coverage
    """
    print(f"\n" + "=" * 50)
    print("🔍 CHECKING CENSUS COVERAGE FOR TESSELLATION TRACTS")
    print("=" * 50)
    
    tessellation_tract_ids = [
        '36061021600', '36061018300', '36061007900', '36061019900',
        '36061017900', '36061019100', '36061023400', '36061018700',
        '36061023700', '36061022500'
    ]
    
    # Load census data
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    
    if not os.path.exists(census_file):
        print(f"❌ Census file not found")
        return
    
    census_df = pd.read_csv(census_file)
    census_df['tract_str'] = census_df['tract'].apply(lambda x: str(int(float(x))))
    
    print(f"📊 Checking census coverage for tessellation tracts:")
    
    for tract_id in tessellation_tract_ids:
        if tract_id in census_df['tract_str'].values:
            census_row = census_df[census_df['tract_str'] == tract_id].iloc[0]
            print(f"   {tract_id}: ✅ IN CENSUS (pop: {census_row['population']})")
        else:
            print(f"   {tract_id}: ❌ NOT in census - should use tessellation")

if __name__ == "__main__":
    # Run the analysis
    results = check_specific_tracts()
    
    if results:
        analyze_results(results)
    
    check_census_coverage()
    
    print(f"\n" + "=" * 50)
    print("🎯 CONCLUSION")
    print("=" * 50)
    print("This analysis checks specific NYC tracts that we know have")
    print("tessellation population data to see if they appear correctly")
    print("in the final output or if there are extraction issues.")
