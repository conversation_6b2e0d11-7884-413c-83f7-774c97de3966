#!/usr/bin/env python3
"""
Comprehensive investigation of NYC tessellation coverage and population data extraction
"""

import json
import pandas as pd
import os

def analyze_tessellation_coverage():
    """
    Analyze tessellation file to find NYC tracts with population data
    """
    print("🔍 ANALYZING TESSELLATION DATA COVERAGE FOR NYC TRACTS")
    print("=" * 60)
    
    tessellation_file = 'population files/tessellation_with_population.geojson'
    
    if not os.path.exists(tessellation_file):
        print(f"❌ Tessellation file not found: {tessellation_file}")
        return {}
    
    print(f"📊 Loading tessellation data from: {tessellation_file}")
    
    try:
        with open(tessellation_file, 'r') as f:
            tessellation_data = json.load(f)
        
        features = tessellation_data.get('features', [])
        print(f"📊 Total features: {len(features):,}")
        
        # Analyze tract coverage by state
        tract_coverage = {}
        nyc_tracts_with_population = {}
        
        print(f"\n🔍 Analyzing tract coverage (processing all features)...")
        
        for i, feature in enumerate(features):
            if i % 50000 == 0:
                print(f"   Processed {i:,} features...")
            
            properties = feature.get('properties', {})
            tile_id = properties.get('tile_ID', '')
            population = properties.get('population')
            
            if tile_id and len(str(tile_id)) >= 11:
                tract_id = str(tile_id)[:11]
                state_code = tract_id[:2]
                
                # Count by state
                tract_coverage[state_code] = tract_coverage.get(state_code, 0) + 1
                
                # Collect NYC tracts with population
                if state_code == '36' and population is not None:
                    if tract_id not in nyc_tracts_with_population:
                        nyc_tracts_with_population[tract_id] = []
                    nyc_tracts_with_population[tract_id].append({
                        'tile_ID': tile_id,
                        'population': population,
                        'feature_index': i
                    })
        
        print(f"\n📊 TESSELLATION COVERAGE BY STATE:")
        for state_code, count in sorted(tract_coverage.items()):
            state_name = 'New York' if state_code == '36' else 'Puerto Rico' if state_code == '72' else f'State {state_code}'
            print(f"   {state_code}xxx: {count:,} features ({state_name})")
        
        print(f"\n📊 NYC TRACTS WITH POPULATION DATA:")
        print(f"   Unique NYC tracts with population: {len(nyc_tracts_with_population):,}")
        
        # Show examples
        sample_nyc_tracts = list(nyc_tracts_with_population.keys())[:10]
        print(f"\n   Sample NYC tracts with tessellation population:")
        for tract_id in sample_nyc_tracts:
            tract_data = nyc_tracts_with_population[tract_id][0]  # First occurrence
            print(f"     {tract_id}: population={tract_data['population']}")
        
        return nyc_tracts_with_population
        
    except Exception as e:
        print(f"❌ Error loading tessellation data: {e}")
        return {}

def find_missing_population_examples(nyc_tessellation_tracts):
    """
    Find NYC tracts that have tessellation population but missing data in output
    """
    print(f"\n" + "=" * 60)
    print("🔍 FINDING MISSING POPULATION EXAMPLES")
    print("=" * 60)
    
    # Load output file
    output_file = 'monthly_output_files/tract_flows_with_population_2024-month-01.csv'
    
    if not os.path.exists(output_file):
        print(f"❌ Output file not found: {output_file}")
        return []
    
    print(f"📊 Loading output file: {output_file}")
    df = pd.read_csv(output_file)
    
    # Get all tract IDs from output
    all_source_tracts = set(df['Source'].dropna().astype(str))
    all_dest_tracts = set(df['Destination'].dropna().astype(str))
    all_output_tracts = all_source_tracts.union(all_dest_tracts)
    
    print(f"📊 Total unique tracts in output: {len(all_output_tracts):,}")
    
    # Find NYC tracts in output
    nyc_output_tracts = {t for t in all_output_tracts if t.startswith('36')}
    print(f"📊 NYC tracts in output: {len(nyc_output_tracts):,}")
    
    # Find intersection: NYC tracts with tessellation population that appear in output
    tessellation_tracts_set = set(nyc_tessellation_tracts.keys())
    intersection_tracts = tessellation_tracts_set.intersection(nyc_output_tracts)
    
    print(f"📊 NYC tracts with tessellation population that appear in output: {len(intersection_tracts):,}")
    
    if len(intersection_tracts) == 0:
        print(f"❌ No overlap found between tessellation tracts and output tracts")
        return []
    
    # Analyze these tracts in the output
    missing_examples = []
    
    print(f"\n🔍 Analyzing population data for overlapping tracts...")
    
    for tract_id in list(intersection_tracts)[:20]:  # Check first 20
        # Check as source tract
        source_rows = df[df['Source'] == tract_id]
        if len(source_rows) > 0:
            source_pop_null = source_rows['Source_Population'].isna().any()
            source_coord_source = source_rows['Source_Coordinate_Source'].iloc[0]
            
            if source_pop_null:
                tessellation_pop = nyc_tessellation_tracts[tract_id][0]['population']
                missing_examples.append({
                    'tract_id': tract_id,
                    'role': 'source',
                    'tessellation_population': tessellation_pop,
                    'output_population': None,
                    'coordinate_source': source_coord_source,
                    'sample_row': source_rows.iloc[0].to_dict()
                })
        
        # Check as destination tract
        dest_rows = df[df['Destination'] == tract_id]
        if len(dest_rows) > 0:
            dest_pop_null = dest_rows['Destination_Population'].isna().any()
            dest_coord_source = dest_rows['Destination_Coordinate_Source'].iloc[0]
            
            if dest_pop_null:
                tessellation_pop = nyc_tessellation_tracts[tract_id][0]['population']
                missing_examples.append({
                    'tract_id': tract_id,
                    'role': 'destination',
                    'tessellation_population': tessellation_pop,
                    'output_population': None,
                    'coordinate_source': dest_coord_source,
                    'sample_row': dest_rows.iloc[0].to_dict()
                })
    
    print(f"\n📊 MISSING POPULATION EXAMPLES FOUND: {len(missing_examples)}")
    
    if len(missing_examples) > 0:
        print(f"\n🚨 SPECIFIC EXAMPLES OF MISSING TESSELLATION POPULATION:")
        for i, example in enumerate(missing_examples[:5]):
            print(f"\n   Example {i+1}:")
            print(f"     Tract ID: {example['tract_id']}")
            print(f"     Role: {example['role']}")
            print(f"     Tessellation population: {example['tessellation_population']}")
            print(f"     Output population: {example['output_population']}")
            print(f"     Coordinate source: {example['coordinate_source']}")
    else:
        print(f"\n✅ No missing population examples found")
        print(f"   This suggests the tessellation population extraction is working correctly")
    
    return missing_examples

def check_census_coverage_for_tessellation_tracts(nyc_tessellation_tracts):
    """
    Check if tessellation tracts have census population data (explaining why tessellation isn't used)
    """
    print(f"\n" + "=" * 60)
    print("🔍 CHECKING CENSUS COVERAGE FOR TESSELLATION TRACTS")
    print("=" * 60)
    
    # Load census data
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    
    if not os.path.exists(census_file):
        print(f"❌ Census file not found: {census_file}")
        return
    
    print(f"📊 Loading census data: {census_file}")
    census_df = pd.read_csv(census_file)
    
    # Convert census tract IDs to same format
    census_df['tract_str'] = census_df['tract'].apply(lambda x: str(int(float(x))))
    census_tracts_with_pop = set(census_df[census_df['population'].notna()]['tract_str'])
    
    print(f"📊 Census tracts with population: {len(census_tracts_with_pop):,}")
    
    # Check overlap
    tessellation_tract_set = set(nyc_tessellation_tracts.keys())
    overlap = tessellation_tract_set.intersection(census_tracts_with_pop)
    tessellation_only = tessellation_tract_set - census_tracts_with_pop
    
    print(f"\n📊 OVERLAP ANALYSIS:")
    print(f"   Tessellation tracts: {len(tessellation_tract_set):,}")
    print(f"   Census tracts with population: {len(census_tracts_with_pop):,}")
    print(f"   Overlap (both tessellation and census): {len(overlap):,}")
    print(f"   Tessellation-only (no census population): {len(tessellation_only):,}")
    
    if len(tessellation_only) > 0:
        print(f"\n🎯 TESSELLATION-ONLY TRACTS (should use tessellation population):")
        sample_tess_only = list(tessellation_only)[:10]
        for tract_id in sample_tess_only:
            tessellation_pop = nyc_tessellation_tracts[tract_id][0]['population']
            print(f"     {tract_id}: tessellation_population={tessellation_pop}")
        
        return list(tessellation_only)
    else:
        print(f"\n✅ All tessellation tracts also have census population data")
        print(f"   This explains why tessellation population isn't used (census has higher priority)")
        return []

def trace_specific_tract_data_flow(tract_id, tessellation_tracts):
    """
    Trace the complete data flow for a specific tract
    """
    print(f"\n" + "=" * 60)
    print(f"🔍 TRACING DATA FLOW FOR TRACT {tract_id}")
    print("=" * 60)
    
    if tract_id not in tessellation_tracts:
        print(f"❌ Tract {tract_id} not found in tessellation data")
        return
    
    tessellation_data = tessellation_tracts[tract_id][0]
    print(f"📊 Tessellation data for {tract_id}:")
    print(f"   Population: {tessellation_data['population']}")
    print(f"   tile_ID: {tessellation_data['tile_ID']}")
    
    # Check census data
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_df['tract_str'] = census_df['tract'].apply(lambda x: str(int(float(x))))
        
        if tract_id in census_df['tract_str'].values:
            census_row = census_df[census_df['tract_str'] == tract_id].iloc[0]
            print(f"📊 Census data for {tract_id}:")
            print(f"   Population: {census_row['population']}")
            print(f"   Coordinates: ({census_row['latitude']}, {census_row['longitude']})")
            print(f"   🎯 EXPECTED: Census population should be used (higher priority)")
        else:
            print(f"📊 Census data for {tract_id}: NOT FOUND")
            print(f"   🎯 EXPECTED: Tessellation population should be used")
    
    # Check in output
    output_file = 'monthly_output_files/tract_flows_with_population_2024-month-01.csv'
    if os.path.exists(output_file):
        df = pd.read_csv(output_file)
        
        # Check as source
        source_rows = df[df['Source'] == tract_id]
        if len(source_rows) > 0:
            sample_row = source_rows.iloc[0]
            print(f"📊 Output data for {tract_id} as SOURCE:")
            print(f"   Population: {sample_row['Source_Population']}")
            print(f"   Coordinate source: {sample_row['Source_Coordinate_Source']}")
        
        # Check as destination
        dest_rows = df[df['Destination'] == tract_id]
        if len(dest_rows) > 0:
            sample_row = dest_rows.iloc[0]
            print(f"📊 Output data for {tract_id} as DESTINATION:")
            print(f"   Population: {sample_row['Destination_Population']}")
            print(f"   Coordinate source: {sample_row['Destination_Coordinate_Source']}")
        
        if len(source_rows) == 0 and len(dest_rows) == 0:
            print(f"📊 Output data for {tract_id}: NOT FOUND in flow data")

if __name__ == "__main__":
    print("🔍 COMPREHENSIVE NYC TESSELLATION INVESTIGATION")
    print("=" * 70)
    
    # Step 1: Analyze tessellation coverage
    nyc_tessellation_tracts = analyze_tessellation_coverage()
    
    if len(nyc_tessellation_tracts) == 0:
        print("❌ No NYC tracts with tessellation population found")
        exit()
    
    # Step 2: Find missing population examples
    missing_examples = find_missing_population_examples(nyc_tessellation_tracts)
    
    # Step 3: Check census coverage
    tessellation_only_tracts = check_census_coverage_for_tessellation_tracts(nyc_tessellation_tracts)
    
    # Step 4: Trace specific examples
    if len(tessellation_only_tracts) > 0:
        print(f"\n" + "=" * 70)
        print("🔍 TRACING SPECIFIC TESSELLATION-ONLY TRACTS")
        print("=" * 70)
        
        for tract_id in tessellation_only_tracts[:3]:  # Trace first 3
            trace_specific_tract_data_flow(tract_id, nyc_tessellation_tracts)
    
    # Final summary
    print(f"\n" + "=" * 70)
    print("🎯 INVESTIGATION SUMMARY")
    print("=" * 70)
    
    print(f"✅ Tessellation data coverage verified:")
    print(f"   - NYC tracts with tessellation population: {len(nyc_tessellation_tracts):,}")
    print(f"   - Missing population examples found: {len(missing_examples)}")
    print(f"   - Tessellation-only tracts (no census): {len(tessellation_only_tracts)}")
    
    if len(missing_examples) > 0:
        print(f"\n🚨 POTENTIAL ISSUES IDENTIFIED:")
        print(f"   - {len(missing_examples)} cases of missing tessellation population in output")
        print(f"   - Recommend debugging with tract: {missing_examples[0]['tract_id']}")
    elif len(tessellation_only_tracts) > 0:
        print(f"\n🔍 RECOMMENDED DEBUG TRACT:")
        print(f"   - Use tract: {tessellation_only_tracts[0]}")
        print(f"   - This tract has tessellation population but no census data")
        print(f"   - Should demonstrate tessellation population extraction")
    else:
        print(f"\n✅ NO ISSUES FOUND:")
        print(f"   - All tessellation tracts also have census population")
        print(f"   - Census data takes priority (correct behavior)")
        print(f"   - Tessellation system working as designed")
