#!/usr/bin/env python3
"""
Monthly Tract Flow Analysis Pipeline

This script processes monthly aggregated input files and generates monthly tract flow output files.
It includes all recent modifications:
- Updated coordinate hierarchy (census → gazetteer → tessellation)
- Multi-polygon centroid calculation for tessellation data
- Destination coordinate source tracking

Modified from process_tract_flows.py for monthly processing.

Author: Augment Agent
Date: 2024
"""

import pandas as pd
import numpy as np # For np.nan
import re
import json
import os
import glob
import gzip
from math import radians, sin, cos, sqrt, atan2
from typing import Dict, Tuple, List, Any, Optional, Callable
import logging

# --- Constants ---
CENSUS_POPULATION_FILE = 'population files/census-populations-2020-tract-new-york.csv'
GAZETTEER_FILE = '2020_Gaz_tracts_national.txt'
TESSELLATION_FILE = 'population files/tessellation_with_population.geojson'
INPUT_FILES_PATTERN = 'NYC-2024-monthly-aggregated/*.csv.gz'
OUTPUT_DIR = 'monthly_output_files'

# Earth radius in kilometers
EARTH_RADIUS_KM = 6371

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --- Helper Functions ---

def haversine_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate distance between two points using Haversine formula."""
    if any(pd.isna(coord) for coord in [lat1, lon1, lat2, lon2]):
        return np.nan

    lat1_rad, lon1_rad, lat2_rad, lon2_rad = map(radians, [lat1, lon1, lat2, lon2])

    dlon = lon2_rad - lon1_rad
    dlat = lat2_rad - lat1_rad
    a = sin(dlat / 2)**2 + cos(lat1_rad) * cos(lat2_rad) * sin(dlon / 2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    return EARTH_RADIUS_KM * c


def parse_visitor_home_cbgs(json_str: Optional[str]) -> Dict[str, int]:
    """
    Parse visitor_home_cbgs string.
    The regex '""""(\d+)"""":([\d]+)' is highly specific to the input format.
    """
    if pd.isna(json_str) or json_str == '{}' or not isinstance(json_str, str):
        return {}

    # Attempt standard JSON parsing first if quotes are standard
    try:
        # A common SafeGraph pattern is a stringified JSON where keys are also strings
        # e.g. "{\"123\": 4, \"567\": 8}"
        data = json.loads(json_str)
        if isinstance(data, dict): # Check if it's a dict
             # Ensure keys are strings and values are integers
            return {str(k): int(v) for k, v in data.items()}
    except json.JSONDecodeError:
        # Fallback to regex for the specific "quad-quoted" format
        pass # Proceed to regex parsing

    # Regex for the specific "quad-quoted" format: """"<CBG>"""":<COUNT>
    pattern = r'""""(\d+)"""":([\d]+)'
    matches = re.findall(pattern, json_str)
    return {cbg: int(count) for cbg, count in matches}


# --- Data Loading Functions ---

def _load_census_data(
    filepath: str,
    existing_coords: Dict[str, Dict[str, float]],
    existing_populations: Dict[str, float],
    coord_sources: Dict[str, str]
) -> None:
    """Loads coordinate and population data from census CSV."""
    logging.info("  Priority 1: Loading census data...")
    if not os.path.exists(filepath):
        logging.warning(f"    Census file not found: {filepath}")
        return

    census_df = pd.read_csv(filepath)
    loaded_coords_count = 0
    for _, row in census_df.iterrows():
        try:
            tract_id = str(int(float(row['tract'])))
        except ValueError:
            logging.warning(f"    Could not parse tract_id from census row: {row['tract']}")
            continue
            
        if pd.notna(row['latitude']) and pd.notna(row['longitude']):
            if tract_id not in existing_coords: # Highest priority
                existing_coords[tract_id] = {'latitude': row['latitude'], 'longitude': row['longitude']}
                coord_sources[tract_id] = 'census'
                loaded_coords_count += 1
        if pd.notna(row['population']):
            if tract_id not in existing_populations: # Highest priority
                existing_populations[tract_id] = float(row['population'])
    logging.info(f"    Loaded {loaded_coords_count} new coordinates and relevant populations from census data.")


def _load_gazetteer_data(
    filepath: str,
    existing_coords: Dict[str, Dict[str, float]],
    coord_sources: Dict[str, str]
) -> None:
    """Loads coordinate data from gazetteer TXT."""
    logging.info("  Priority 2: Loading gazetteer data...")
    if not os.path.exists(filepath):
        logging.warning(f"    Gazetteer file not found: {filepath}")
        return

    loaded_coords_count = 0
    with open(filepath, 'r', encoding='utf-8') as f:
        next(f)  # Skip header
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 8: # GEOID, INTPTLAT, INTPTLON are usually parts[1], parts[6], parts[7]
                tract_id = parts[1]
                if tract_id not in existing_coords: # Only add if not already present
                    try:
                        lat = float(parts[6]) # INTPTLAT - Internal Point Latitude
                        lon = float(parts[7]) # INTPTLON - Internal Point Longitude
                        if -90 <= lat <= 90 and -180 <= lon <= 180:
                             existing_coords[tract_id] = {'latitude': lat, 'longitude': lon}
                             coord_sources[tract_id] = 'gazetteer'
                             loaded_coords_count += 1
                        else:
                            logging.debug(f"    Gazetteer lat/lon out of bounds for tract {tract_id}: ({lat}, {lon})")
                    except (ValueError, IndexError):
                        logging.debug(f"    Could not parse lat/lon for gazetteer tract {tract_id}")
                        continue
    logging.info(f"    Added {loaded_coords_count} new coordinates from gazetteer data.")


def _calculate_polygon_centroid(coords: List[List[float]]) -> Optional[Tuple[float, float]]:
    """
    Calculates the centroid of a polygon's vertices.
    Note: This is the centroid of the vertices, not necessarily the geometric centroid of the polygon area.
    For more accurate geometric centroids, a library like Shapely might be needed.
    """
    valid_coords = [
        coord for coord in coords
        if len(coord) >= 2 and
           all(c is not None and not pd.isna(c) and c != 0 for c in coord[:2])
    ]
    if not valid_coords:
        return None
    
    sum_x = sum(p[0] for p in valid_coords)
    sum_y = sum(p[1] for p in valid_coords)
    count = len(valid_coords)
    centroid_lon = sum_x / count
    centroid_lat = sum_y / count
    
    if not (-90 <= centroid_lat <= 90 and -180 <= centroid_lon <= 180):
        logging.debug(f"    Calculated tessellation centroid out of bounds: ({centroid_lat}, {centroid_lon})")
        return None
    return centroid_lat, centroid_lon


def _load_tessellation_data(
    filepath: str,
    existing_coords: Dict[str, Dict[str, float]],
    existing_populations: Dict[str, float],
    coord_sources: Dict[str, str]
) -> None:
    """Loads and processes tessellation data from GeoJSON."""
    logging.info("  Priority 3: Loading tessellation data...")
    if not os.path.exists(filepath):
        logging.warning(f"    Tessellation file not found: {filepath}")
        return

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            tessellation_data = json.load(f)
    except Exception as e:
        logging.error(f"    Error loading tessellation GeoJSON: {e}")
        return

    temp_centroids_per_tract: Dict[str, List[Dict[str, float]]] = {}
    temp_populations_per_tract: Dict[str, List[float]] = {} # Stores lists of population values per tract
    
    for feature in tessellation_data.get('features', []):
        properties = feature.get('properties', {})
        tile_id_str = properties.get('tile_ID')
        if not tile_id_str: # Ensure tile_ID is not None or empty
            continue
        tile_id_str = str(tile_id_str) # Ensure it's a string

        if len(tile_id_str) >= 11:
            tract_id = tile_id_str[:11]

            # Process coordinates only if not already found from higher priority sources
            if tract_id not in existing_coords:
                geometry = feature.get('geometry', {})
                if geometry.get('type') == 'Polygon':
                    # GeoJSON polygons have an outer ring and optional inner rings.
                    # We are interested in the outer ring: coordinates[0]
                    polygon_vertex_coords = geometry.get('coordinates', [[]])[0]
                    if polygon_vertex_coords:
                        centroid = _calculate_polygon_centroid(polygon_vertex_coords)
                        if centroid:
                            temp_centroids_per_tract.setdefault(tract_id, []).append(
                                {'lat': centroid[0], 'lon': centroid[1]}
                            )
            
            # Collect population data from all tiles for aggregation, if tract not in existing_populations
            if tract_id not in existing_populations:
                population = properties.get('population')
                if population is not None:
                    try:
                        pop_value = float(population)
                        if pd.notna(pop_value): # Ensure it's not NaN after conversion
                             temp_populations_per_tract.setdefault(tract_id, []).append(pop_value)
                    except (ValueError, TypeError):
                        logging.debug(f"    Could not parse population '{population}' for tile_ID {tile_id_str}")

    # Phase 2: Average centroids for tracts with multiple polygons and add to existing_coords
    added_coords_count = 0
    for tract_id, centroid_list in temp_centroids_per_tract.items():
        if tract_id in existing_coords: # Skip if already added by higher priority
            continue
        if centroid_list:
            avg_lat = sum(c['lat'] for c in centroid_list) / len(centroid_list)
            avg_lon = sum(c['lon'] for c in centroid_list) / len(centroid_list)
            if -90 <= avg_lat <= 90 and -180 <= avg_lon <= 180:
                existing_coords[tract_id] = {'latitude': avg_lat, 'longitude': avg_lon}
                coord_sources[tract_id] = 'tessellation'
                added_coords_count += 1
            else:
                logging.debug(f"    Averaged tessellation centroid out of bounds for {tract_id}: ({avg_lat}, {avg_lon})")
    logging.info(f"    Added {added_coords_count} new coordinates from tessellation data.")

    # Phase 3: Aggregate population data and add to existing_populations
    added_populations_count = 0
    for tract_id, pop_list in temp_populations_per_tract.items():
        if tract_id in existing_populations: # Skip if already added by higher priority (census)
            continue
        if pop_list:
            aggregated_population = sum(pop_list)
            existing_populations[tract_id] = aggregated_population
            added_populations_count +=1
            # If population is added from tessellation, but coords weren't (e.g. higher prio coords exist),
            # ensure coord_sources still reflects a tessellation contribution if no other source set it.
            if tract_id not in coord_sources and tract_id in existing_coords : # If coords exist from Gaz or Census
                 pass # Keep existing source
            elif tract_id not in coord_sources and tract_id not in existing_coords: # If no coords exist yet
                 # This case implies population from tessellation, but no valid geometry for centroid from tessellation.
                 # We won't set coord_source here, as there are no coords.
                 pass
            # If only population from tessellation is useful, and coords came from higher source, source remains higher.
            # If coords also came from tessellation, coord_sources[tract_id] is already 'tessellation'.
    logging.info(f"    Added {added_populations_count} new population values from tessellation data.")


def load_coordinate_and_population_data() -> Tuple[Dict[str, Dict[str, float]], Dict[str, float], Dict[str, str]]:
    """
    Loads coordinate and population data from all sources using the 3-tier hierarchy.
    Returns:
        tract_coords: Mapping tract_id to {'latitude': lat, 'longitude': lon}.
        tract_populations: Mapping tract_id to population.
        coord_sources: Mapping tract_id to its coordinate data source ('census', 'gazetteer', 'tessellation').
    """
    tract_coords: Dict[str, Dict[str, float]] = {}
    tract_populations: Dict[str, float] = {}
    coord_sources: Dict[str, str] = {}

    logging.info("Loading coordinate and population data with updated hierarchy...")

    _load_census_data(CENSUS_POPULATION_FILE, tract_coords, tract_populations, coord_sources)
    _load_gazetteer_data(GAZETTEER_FILE, tract_coords, coord_sources) # Gazetteer only provides coords
    _load_tessellation_data(TESSELLATION_FILE, tract_coords, tract_populations, coord_sources)
    
    logging.info(f"  Total unique coordinates loaded: {len(tract_coords)}")
    logging.info(f"  Total unique population data loaded: {len(tract_populations)}")
    
    # Log source distribution
    source_counts = {src: 0 for src in ['census', 'gazetteer', 'tessellation', 'unknown']}
    for tid in tract_coords:
        source_counts[coord_sources.get(tid, 'unknown')] += 1
    logging.info(f"  Coordinate source distribution: {source_counts}")

    pop_source_counts = {'census': 0, 'tessellation': 0, 'unknown_source_pop':0}
    temp_census_pop_ids = set()
    # Re-check population sources based on final tract_populations and coord_sources
    # Need to know which populations came from census vs tessellation *after* hierarchy applied
    # For census, population and coords must both be census-sourced if pop came from census file.
    # For tessellation, population came from tessellation file. Coords could be census, gaz, or tess.

    # Simplified logic: if tract_id in tract_populations, its source is determined by coord_sources
    # for priority. If census provided pop, it got in first. If tess provided pop AND census didn't, it got in.
    # This means `tract_populations` correctly reflects the hierarchy.
    # To report counts by source of population:
    # Create temporary census population from file to cross-check
    temp_census_pop_from_file = {}
    if os.path.exists(CENSUS_POPULATION_FILE):
        _cdf = pd.read_csv(CENSUS_POPULATION_FILE)
        for _, row in _cdf.iterrows():
            try:
                _tid = str(int(float(row['tract'])))
                if pd.notna(row['population']):
                    temp_census_pop_from_file[_tid] = float(row['population'])
            except ValueError:
                continue

    for tid, pop_val in tract_populations.items():
        if tid in temp_census_pop_from_file and temp_census_pop_from_file[tid] == pop_val:
            pop_source_counts['census'] += 1
        else: # If not from census file directly, or value differs, assume tessellation by hierarchy if present
            pop_source_counts['tessellation'] +=1 # This assumes non-census population is tessellation

    logging.info(f"  Population data by source (estimated): Census={pop_source_counts['census']}, Tessellation={pop_source_counts['tessellation']}")


    return tract_coords, tract_populations, coord_sources


# --- Main Processing Logic ---

def _prepare_cbg_flows(df: pd.DataFrame) -> pd.DataFrame:
    """Processes raw data to create CBG-level flows."""
    logging.info("  Extracting columns and processing visitor data...")
    # Ensure essential columns exist
    required_cols = ['poi_cbg', 'visitor_home_cbgs', 'latitude', 'longitude']
    if not all(col in df.columns for col in required_cols):
        missing = [col for col in required_cols if col not in df.columns]
        logging.error(f"    Input CSV is missing required columns: {missing}. Skipping CBG flow preparation.")
        return pd.DataFrame()

    data = df[required_cols + ['raw_visit_counts']].copy() # Add raw_visit_counts if needed for context, not used in cbg_flows directly
    
    cbg_flows_list = []
    for _, row in data.iterrows():
        if pd.isna(row['poi_cbg']) or pd.isna(row['visitor_home_cbgs']) or \
           pd.isna(row['latitude']) or pd.isna(row['longitude']):
            continue

        poi_cbg = str(int(float(row['poi_cbg']))) if pd.notna(row['poi_cbg']) else None # Ensure CBG is string
        if not poi_cbg: continue

        visitor_home_cbgs_dict = parse_visitor_home_cbgs(row['visitor_home_cbgs'])

        for source_cbg, flow_count in visitor_home_cbgs_dict.items():
            cbg_flows_list.append({
                'source_cbg': str(source_cbg), # Ensure source_cbg is string
                'destination_cbg': poi_cbg,
                'flow': flow_count,
                'dest_lat_poi': row['latitude'], # POI coordinates
                'dest_lon_poi': row['longitude']
            })
    
    return pd.DataFrame(cbg_flows_list)

def _aggregate_to_tract_flows(cbg_flow_df: pd.DataFrame) -> pd.DataFrame:
    """Aggregates CBG flows to tract-level flows."""
    if cbg_flow_df.empty:
        return pd.DataFrame()
    
    logging.info("  Aggregating to tract level...")
    cbg_flow_df['source_tract'] = cbg_flow_df['source_cbg'].astype(str).str[:11]
    cbg_flow_df['destination_tract'] = cbg_flow_df['destination_cbg'].astype(str).str[:11]

    # When grouping, take the first POI's lat/lon as representative for the destination tract's initial coords
    tract_flow_df = cbg_flow_df.groupby(['source_tract', 'destination_tract']).agg(
        flow=('flow', 'sum'),
        # These are initial dest coords from POI, might be overridden by census/gaz/tess later
        dest_lat_initial=('dest_lat_poi', 'first'),
        dest_lon_initial=('dest_lon_poi', 'first')
    ).reset_index()
    return tract_flow_df

def _add_coordinates_and_populations(
    tract_flow_df: pd.DataFrame,
    tract_coords: Dict[str, Dict[str, float]],
    tract_populations: Dict[str, float],
    coord_sources: Dict[str, str]
) -> pd.DataFrame:
    """Adds source/destination coordinates, populations, and their sources to the tract flow DataFrame."""
    if tract_flow_df.empty:
        return tract_flow_df

    logging.info("  Adding coordinates and populations to tract flows...")

    # Source attributes
    tract_flow_df['Source_Latitude'] = tract_flow_df['source_tract'].map(lambda tid: tract_coords.get(tid, {}).get('latitude'))
    tract_flow_df['Source_Longitude'] = tract_flow_df['source_tract'].map(lambda tid: tract_coords.get(tid, {}).get('longitude'))
    tract_flow_df['Source_Coordinate_Source'] = tract_flow_df['source_tract'].map(lambda tid: coord_sources.get(tid, 'not_found'))
    tract_flow_df['Source_Population'] = tract_flow_df['source_tract'].map(tract_populations.get)

    # Destination attributes - respecting hierarchy (census/gaz/tess preferred over POI)
    dest_hier_lat = tract_flow_df['destination_tract'].map(lambda tid: tract_coords.get(tid, {}).get('latitude'))
    dest_hier_lon = tract_flow_df['destination_tract'].map(lambda tid: tract_coords.get(tid, {}).get('longitude'))
    dest_hier_source = tract_flow_df['destination_tract'].map(lambda tid: coord_sources.get(tid))

    tract_flow_df['Destination_Latitude'] = dest_hier_lat.fillna(tract_flow_df['dest_lat_initial'])
    tract_flow_df['Destination_Longitude'] = dest_hier_lon.fillna(tract_flow_df['dest_lon_initial'])
    tract_flow_df['Destination_Coordinate_Source'] = dest_hier_source.fillna('poi_original') # If not in hierarchy, it's from POI
    
    tract_flow_df['Destination_Population'] = tract_flow_df['destination_tract'].map(tract_populations.get)
    
    # Drop intermediate columns
    tract_flow_df.drop(columns=['dest_lat_initial', 'dest_lon_initial'], inplace=True, errors='ignore')
    
    return tract_flow_df

def _calculate_distances(tract_flow_df: pd.DataFrame) -> pd.DataFrame:
    """Calculates Haversine distance between source and destination."""
    if tract_flow_df.empty:
        return tract_flow_df
        
    logging.info("  Calculating source-destination distances...")
    
    def haversine_on_row(row: pd.Series) -> float:
        return haversine_distance(
            row['Source_Latitude'], row['Source_Longitude'],
            row['Destination_Latitude'], row['Destination_Longitude']
        )
    
    tract_flow_df['Source_Destination_Distance'] = tract_flow_df.apply(haversine_on_row, axis=1)
    return tract_flow_df


def _calculate_buffer_stats_for_row(
    row: pd.Series,
    tract_to_pois_map: Dict[str, List[str]],
    all_tract_coords_map: Dict[str, Dict[str, float]],
    all_tract_populations_map: Dict[str, float],
    # coord_sources_map: Dict[str, str] # Not directly used here, but passed for consistency if needed
) -> pd.Series:
    """Helper function for df.apply() to calculate buffer stats for a single flow row."""
    source_tract = row['source_tract']
    dest_tract = row['destination_tract']
    # Use the final chosen dest lat/lon
    dest_lat = row['Destination_Latitude']
    dest_lon = row['Destination_Longitude']
    flow_distance = row['Source_Destination_Distance']

    buffer_poi_count = 0
    population_within_circle = 0
    missing_population_tracts = 0

    if pd.isna(dest_lat) or pd.isna(dest_lon) or pd.isna(flow_distance) or flow_distance <= 0:
        return pd.Series({
            'Buffer_POI_Count': buffer_poi_count,
            'Population_within_Circle': population_within_circle,
            'Missing_Population_Tracts_in_Buffer': missing_population_tracts
        })

    for other_tract_id, pois_in_other_tract in tract_to_pois_map.items():
        if other_tract_id == source_tract or other_tract_id == dest_tract:
            continue

        other_tract_coord_info = all_tract_coords_map.get(other_tract_id)
        if not other_tract_coord_info or pd.isna(other_tract_coord_info.get('latitude')):
            continue 

        other_tract_lat = other_tract_coord_info['latitude']
        other_tract_lon = other_tract_coord_info['longitude']
        
        # Calculate distance from current flow's destination to this 'other_tract'
        dist_to_other_tract = haversine_distance(dest_lat, dest_lon, other_tract_lat, other_tract_lon)

        if pd.notna(dist_to_other_tract) and dist_to_other_tract <= flow_distance:
            buffer_poi_count += len(pois_in_other_tract)
            
            tract_pop = all_tract_populations_map.get(other_tract_id)
            if tract_pop is not None and pd.notna(tract_pop):
                population_within_circle += tract_pop
            else:
                missing_population_tracts += 1
    
    return pd.Series({
        'Buffer_POI_Count': buffer_poi_count,
        'Population_within_Circle': population_within_circle,
        'Missing_Population_Tracts_in_Buffer': missing_population_tracts
    })


def _calculate_buffer_and_circle_population(
    tract_flow_df: pd.DataFrame,
    tract_to_pois: Dict[str, List[str]], # Maps tract_id to list of placekeys
    tract_coords: Dict[str, Dict[str, float]],
    tract_populations: Dict[str, float],
    # coord_sources: Dict[str, str] # Pass if needed by _calculate_buffer_stats_for_row
)-> pd.DataFrame:
    """Calculates buffer POI counts and population within the circle."""
    if tract_flow_df.empty:
        return tract_flow_df
        
    logging.info("  Calculating buffer POI counts and population within circle...")
    
    # Apply the calculation row-wise
    # Note: .apply with axis=1 is essentially a loop, but cleaner pandas syntax.
    # For very large DataFrames, further optimization might be needed here (e.g. spatial indexing).
    buffer_stats = tract_flow_df.apply(
        _calculate_buffer_stats_for_row,
        axis=1,
        tract_to_pois_map=tract_to_pois,
        all_tract_coords_map=tract_coords,
        all_tract_populations_map=tract_populations
        # coord_sources_map=coord_sources
    )
    tract_flow_df = pd.concat([tract_flow_df, buffer_stats], axis=1)
    return tract_flow_df


def process_single_file(
    input_file: str,
    output_dir: str,
    tract_coords: Dict[str, Dict[str, float]],
    tract_populations: Dict[str, float],
    coord_sources: Dict[str, str]
) -> None:
    """Processes a single monthly aggregated input file."""
    file_basename = os.path.basename(input_file)
    date_match = re.search(r'NYC-2024-monthly-(\d{2})', file_basename)
    date_str = f'2024-month-{date_match.group(1)}' if date_match else "unknown_date"
    output_file = os.path.join(output_dir, f"tract_flows_with_population_{date_str}.csv")

    logging.info(f"\nProcessing file: {file_basename}")
    logging.info(f"Output will be saved to: {output_file}")

    try:
        logging.info("  Reading data...")
        with gzip.open(input_file, mode='rt', encoding='utf-8', errors='replace') as f:
            raw_df = pd.read_csv(f)
    except Exception as e:
        logging.error(f"  Error reading gzipped CSV {input_file}: {e}")
        return

    # 1. Create CBG-level flows
    cbg_flow_df = _prepare_cbg_flows(raw_df)
    if cbg_flow_df.empty:
        logging.warning("  No valid CBG flows found. Skipping this file.")
        return

    # 2. Aggregate to Tract-level flows
    tract_flow_df = _aggregate_to_tract_flows(cbg_flow_df)
    if tract_flow_df.empty:
        logging.warning("  No valid Tract flows after aggregation. Skipping this file.")
        return
        
    # 3. Create tract_to_pois mapping (used for buffer calculation)
    # Ensure 'poi_cbg' and 'placekey' are in raw_df
    if 'poi_cbg' not in raw_df.columns or 'placekey' not in raw_df.columns:
        logging.error("  'poi_cbg' or 'placekey' not in input CSV. Cannot create tract_to_pois map.")
        return
        
    logging.info("  Creating tract to POI mapping...")
    # Use .loc to avoid SettingWithCopyWarning, ensure 'poi_cbg' is clean
    raw_df_copy = raw_df[['poi_cbg', 'placekey']].dropna(subset=['poi_cbg', 'placekey']).copy()
    raw_df_copy.loc[:, 'tract'] = raw_df_copy['poi_cbg'].astype(float).astype(int).astype(str).str[:11]
    tract_to_pois = raw_df_copy.groupby('tract')['placekey'].apply(list).to_dict()

    # 4. Add coordinates and populations
    tract_flow_df = _add_coordinates_and_populations(tract_flow_df, tract_coords, tract_populations, coord_sources)

    # 5. Calculate distances
    tract_flow_df = _calculate_distances(tract_flow_df)
    
    # 6. Calculate buffer POI counts and population within circle
    tract_flow_df = _calculate_buffer_and_circle_population(
        tract_flow_df, tract_to_pois, tract_coords, tract_populations #, coord_sources
    )

    # 7. Select and rename columns for final output
    final_columns_map = {
        'source_tract': 'Source',
        'destination_tract': 'Destination',
        'flow': 'AggFlow',
        'Source_Latitude': 'Source_Latitude',
        'Source_Longitude': 'Source_Longitude',
        'Source_Coordinate_Source': 'Source_Coordinate_Source',
        'Source_Population': 'Source_Population',
        'Destination_Latitude': 'Destination_Latitude',
        'Destination_Longitude': 'Destination_Longitude',
        'Destination_Coordinate_Source': 'Destination_Coordinate_Source',
        'Destination_Population': 'Destination_Population',
        'Source_Destination_Distance': 'Source_Destination_Distance',
        'Buffer_POI_Count': 'Buffer_POI_Count', # Renamed from 'buffer'
        'Population_within_Circle': 'Population_within_Circle',
        'Missing_Population_Tracts_in_Buffer': 'Missing_Population_Tracts_in_Buffer' # Renamed
    }
    # Ensure all expected columns exist before selecting, fill missing ones with NaN or default
    for col_original in final_columns_map.keys():
        if col_original not in tract_flow_df.columns:
            tract_flow_df[col_original] = np.nan # Or appropriate default

    final_df = tract_flow_df[list(final_columns_map.keys())].rename(columns=final_columns_map)

    logging.info(f"  Saving tract level output to {output_file}...")
    final_df.to_csv(output_file, index=False, float_format='%.6f') # Control float precision

    logging.info(f"Done! Tract-level flows for {date_str} have been saved.")
    logging.info("\nSample of final tract level flows:")
    logging.info(f"\n{final_df.head().to_string()}")

    # Statistics
    logging.info("\n  Population Calculation Statistics:")
    total_pairs = len(final_df)
    if total_pairs > 0:
        src_pop_ok = final_df['Source_Population'].notna().sum()
        dest_pop_ok = final_df['Destination_Population'].notna().sum()
        pop_circle_ok = (final_df['Population_within_Circle'] > 0).sum()
        logging.info(f"    Total tract pairs: {total_pairs}")
        logging.info(f"    Pairs with source population: {src_pop_ok} ({src_pop_ok/total_pairs*100:.1f}%)")
        logging.info(f"    Pairs with destination population: {dest_pop_ok} ({dest_pop_ok/total_pairs*100:.1f}%)")
        logging.info(f"    Pairs with non-zero population within circle: {pop_circle_ok} ({pop_circle_ok/total_pairs*100:.1f}%)")


def main():
    """Main function to orchestrate the pipeline."""
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # Load master coordinate and population data once
    tract_coords, tract_populations, coord_sources = load_coordinate_and_population_data()

    # Test population lookup for a few sample tracts
    # test_tracts = ['36061003700', '36061008200'] # Example NYC tracts
    # logging.info("\n🧪 Testing population lookup for sample tracts:")
    # for tract_id_test in test_tracts:
    #     pop = tract_populations.get(tract_id_test)
    #     src = coord_sources.get(tract_id_test, 'not_found')
    #     logging.info(f"   {tract_id_test}: population={pop}, coord_source_for_pop_or_coords={src}")


    input_files = glob.glob(INPUT_FILES_PATTERN)
    if not input_files:
        logging.warning(f"No input files found matching pattern: {INPUT_FILES_PATTERN}")
        return
    
    logging.info(f"Found {len(input_files)} input files to process.")

    # For testing, process just one file:
    # if input_files:
    #     input_files = [input_files[0]]
    #     logging.info(f"--- TESTING WITH SUBSET OF FILES: {input_files} ---")

    for input_file_path in input_files:
        process_single_file(input_file_path, OUTPUT_DIR, tract_coords, tract_populations, coord_sources)

    logging.info("\nAll files have been processed. Results are saved in the monthly_output_files directory.")


if __name__ == "__main__":
    main()