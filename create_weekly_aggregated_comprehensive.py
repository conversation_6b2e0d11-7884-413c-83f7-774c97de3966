#!/usr/bin/env python3
"""
Comprehensive Weekly Data Aggregation Pipeline

This script aggregates ALL weekly data files into a single comprehensive dataset.
It follows the same optimization patterns as create_monthly_aggregated_optimized.py
but combines all weekly data across the entire year into one output file.

Features:
- Processes all weekly files from the NYC-2024-weekly directory
- Aggregates data by placekey across all time periods
- Uses memory-efficient chunked processing
- Optimized visitor_home_cbgs merging
- Produces a single comprehensive output file

Author: Augment Agent
Date: 2024
"""

import pandas as pd
import numpy as np
import re
import json
import os
import glob
import gzip
from datetime import datetime
from collections import defaultdict
import subprocess
import sys

def analyze_weekly_files():
    """Analyze all weekly files for comprehensive aggregation"""
    print("🔍 Analyzing all weekly files for comprehensive aggregation...")
    
    # Get all weekly files
    weekly_files = glob.glob('NYC-2024-weekly-20250509T045249Z-1-002/NYC-2024-weekly/*.csv.gz')
    weekly_files = [f for f in weekly_files if 'DATE_RANGE_START' in f]  # Exclude X.csv
    
    print(f"   Found {len(weekly_files)} weekly files")
    
    # Extract dates and sort chronologically
    file_info_list = []
    date_pattern = r'DATE_RANGE_START_(\d{4}-\d{2}-\d{2})'
    
    for file_path in weekly_files:
        filename = os.path.basename(file_path)
        match = re.search(date_pattern, filename)
        if match:
            date_str = match.group(1)
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            file_info_list.append({
                'file': file_path,
                'date': date_str,
                'date_obj': date_obj,
                'filename': filename
            })
    
    # Sort files chronologically
    file_info_list.sort(key=lambda x: x['date_obj'])
    
    print(f"\n📅 Weekly files found (chronological order):")
    for i, file_info in enumerate(file_info_list, 1):
        print(f"   {i:2d}. {file_info['date']} - {file_info['filename']}")
    
    if file_info_list:
        start_date = file_info_list[0]['date']
        end_date = file_info_list[-1]['date']
        print(f"\n📊 Date range: {start_date} to {end_date}")
        print(f"   Total weeks: {len(file_info_list)}")
    
    return file_info_list

def parse_visitor_home_cbgs_fast(json_str):
    """Fast parsing of visitor_home_cbgs - same as monthly script"""
    if pd.isna(json_str) or json_str == '{}' or json_str == '':
        return {}

    try:
        # Use regex to extract CBG and count pairs
        pattern = r'""""(\d+)"""":([\d]+)'
        matches = re.findall(pattern, str(json_str))
        
        result = {}
        for cbg, count in matches:
            result[cbg] = int(count)
        return result
    except:
        return {}

def aggregate_comprehensive_data_optimized(file_info_list):
    """Optimized aggregation of ALL weekly data into a single comprehensive file"""
    print("\n🔄 Aggregating ALL weekly data into comprehensive file (optimized)...")
    
    # Create output directory
    output_dir = 'NYC-2024-comprehensive-aggregated'
    os.makedirs(output_dir, exist_ok=True)
    
    # Use a dictionary to accumulate data by placekey across ALL weeks
    placekey_data = {}
    total_rows_processed = 0
    total_files_processed = 0
    
    print(f"\n📊 Processing {len(file_info_list)} weekly files...")
    
    for i, file_info in enumerate(file_info_list, 1):
        print(f"\n   📖 [{i}/{len(file_info_list)}] Reading {file_info['date']} - {os.path.basename(file_info['file'])}...")
        
        try:
            with gzip.open(file_info['file'], 'rt', encoding='utf-8', errors='replace') as f:
                # Read in chunks to manage memory
                chunk_size = 10000
                file_rows = 0
                
                for chunk in pd.read_csv(f, chunksize=chunk_size):
                    file_rows += len(chunk)
                    total_rows_processed += len(chunk)
                    
                    # Process each row in the chunk
                    for _, row in chunk.iterrows():
                        placekey = row['placekey']
                        
                        if placekey not in placekey_data:
                            # Initialize with first occurrence
                            placekey_data[placekey] = {
                                'placekey': placekey,
                                'poi_cbg': row['poi_cbg'],
                                'raw_visit_counts': 0,
                                'latitude': row['latitude'],
                                'longitude': row['longitude'],
                                'visitor_home_cbgs': {},
                                'other_columns': {},
                                'first_seen': file_info['date'],
                                'weeks_active': set()
                            }
                            
                            # Store other columns from first occurrence
                            for col in chunk.columns:
                                if col not in ['placekey', 'poi_cbg', 'raw_visit_counts', 
                                             'latitude', 'longitude', 'visitor_home_cbgs']:
                                    placekey_data[placekey]['other_columns'][col] = row[col]
                        
                        # Track which weeks this placekey was active
                        placekey_data[placekey]['weeks_active'].add(file_info['date'])
                        
                        # Accumulate raw_visit_counts
                        placekey_data[placekey]['raw_visit_counts'] += row['raw_visit_counts']
                        
                        # Parse and merge visitor_home_cbgs
                        cbgs = parse_visitor_home_cbgs_fast(row['visitor_home_cbgs'])
                        for cbg, count in cbgs.items():
                            if cbg in placekey_data[placekey]['visitor_home_cbgs']:
                                placekey_data[placekey]['visitor_home_cbgs'][cbg] += count
                            else:
                                placekey_data[placekey]['visitor_home_cbgs'][cbg] = count
                
                print(f"      ✅ Processed {file_rows:,} rows from this file")
                total_files_processed += 1
                        
        except Exception as e:
            print(f"      ❌ Error reading file: {e}")
            continue
        
        # Progress update
        if i % 5 == 0 or i == len(file_info_list):
            print(f"   📈 Progress: {i}/{len(file_info_list)} files, {total_rows_processed:,} total rows, {len(placekey_data):,} unique placekeys")
    
    if not placekey_data:
        print(f"   ❌ No data loaded from any files")
        return None
    
    print(f"\n   🎯 Creating comprehensive aggregated DataFrame...")
    
    # Convert accumulated data to DataFrame
    aggregated_data = []
    for placekey, data in placekey_data.items():
        # Format visitor_home_cbgs back to original format
        if data['visitor_home_cbgs']:
            formatted_pairs = [f'""""{cbg}"""":{count}' for cbg, count in data['visitor_home_cbgs'].items()]
            visitor_home_cbgs_str = '{' + ','.join(formatted_pairs) + '}'
        else:
            visitor_home_cbgs_str = '{}'
        
        row_data = {
            'placekey': data['placekey'],
            'poi_cbg': data['poi_cbg'],
            'visitor_home_cbgs': visitor_home_cbgs_str,
            'raw_visit_counts': data['raw_visit_counts'],
            'latitude': data['latitude'],
            'longitude': data['longitude'],
            'weeks_active_count': len(data['weeks_active']),
            'first_seen_date': data['first_seen']
        }
        
        # Add other columns
        row_data.update(data['other_columns'])
        aggregated_data.append(row_data)
    
    # Create DataFrame
    aggregated_df = pd.DataFrame(aggregated_data)
    
    print(f"   ✅ Aggregated to {len(aggregated_df):,} unique placekeys")
    print(f"   📉 Reduction: {total_rows_processed:,} → {len(aggregated_df):,} rows ({len(aggregated_df)/total_rows_processed*100:.1f}%)")
    
    # Generate comprehensive statistics
    print(f"\n📊 Comprehensive aggregation statistics:")
    print(f"   Files processed: {total_files_processed}/{len(file_info_list)}")
    print(f"   Total original rows: {total_rows_processed:,}")
    print(f"   Unique placekeys: {len(aggregated_df):,}")
    print(f"   Data reduction: {(1 - len(aggregated_df)/total_rows_processed)*100:.1f}%")
    
    # Analyze activity patterns
    weeks_active_stats = aggregated_df['weeks_active_count'].describe()
    print(f"\n📈 Placekey activity patterns:")
    print(f"   Average weeks active per placekey: {weeks_active_stats['mean']:.1f}")
    print(f"   Median weeks active: {weeks_active_stats['50%']:.0f}")
    print(f"   Max weeks active: {weeks_active_stats['max']:.0f}")
    print(f"   Min weeks active: {weeks_active_stats['min']:.0f}")

    # Activity distribution analysis
    activity_distribution = aggregated_df['weeks_active_count'].value_counts().sort_index()
    print(f"\n📊 Activity distribution (top patterns):")
    for weeks, count in activity_distribution.head(10).items():
        percentage = (count / len(aggregated_df)) * 100
        print(f"   {weeks:2d} weeks: {count:,} placekeys ({percentage:.1f}%)")

    # Visit count statistics
    visit_stats = aggregated_df['raw_visit_counts'].describe()
    print(f"\n🚶 Visit count statistics:")
    print(f"   Total visits across all placekeys: {aggregated_df['raw_visit_counts'].sum():,}")
    print(f"   Average visits per placekey: {visit_stats['mean']:.0f}")
    print(f"   Median visits per placekey: {visit_stats['50%']:.0f}")
    print(f"   Max visits for a placekey: {visit_stats['max']:.0f}")

    # Save comprehensive aggregated file
    start_date = file_info_list[0]['date'] if file_info_list else 'unknown'
    end_date = file_info_list[-1]['date'] if file_info_list else 'unknown'
    output_filename = f"NYC-2024-comprehensive-weekly-{start_date}_to_{end_date}.csv.gz"
    output_path = os.path.join(output_dir, output_filename)

    print(f"\n   💾 Saving comprehensive file to {output_filename}...")
    with gzip.open(output_path, 'wt', encoding='utf-8') as f:
        aggregated_df.to_csv(f, index=False)

    file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
    print(f"   ✅ Saved {output_filename} ({file_size_mb:.1f} MB)")

    # Store final stats before clearing memory
    unique_placekeys = len(aggregated_df)
    total_visits = int(aggregated_df['raw_visit_counts'].sum())
    avg_weeks_active = float(weeks_active_stats['mean'])

    # Clear memory
    del placekey_data
    del aggregated_data
    del aggregated_df

    return {
        'output_file': output_path,
        'total_files': total_files_processed,
        'total_rows': total_rows_processed,
        'unique_placekeys': unique_placekeys,
        'total_visits': total_visits,
        'avg_weeks_active': avg_weeks_active,
        'file_size_mb': file_size_mb,
        'date_range': f"{start_date} to {end_date}"
    }

def main():
    """Main pipeline execution"""
    print("🚀 COMPREHENSIVE WEEKLY DATA AGGREGATION PIPELINE")
    print("=" * 70)
    
    # Step 1: Analyze all weekly files
    file_info_list = analyze_weekly_files()
    
    if not file_info_list:
        print("❌ No weekly files found!")
        return False
    
    # Step 2: Aggregate ALL weekly data into comprehensive file
    result = aggregate_comprehensive_data_optimized(file_info_list)
    
    if not result:
        print("❌ Comprehensive aggregation failed!")
        return False
    
    print(f"\n✅ COMPREHENSIVE WEEKLY AGGREGATION COMPLETED")
    print(f"   📁 Output file: {os.path.basename(result['output_file'])}")
    print(f"   📊 Processed: {result['total_files']} files")
    print(f"   📈 Data: {result['total_rows']:,} → {result['unique_placekeys']:,} rows")
    print(f"   🚶 Total visits: {result['total_visits']:,}")
    print(f"   📊 Avg weeks active: {result['avg_weeks_active']:.1f}")
    print(f"   💾 File size: {result['file_size_mb']:.1f} MB")
    print(f"   📅 Period: {result['date_range']}")
    print(f"   📂 Location: {os.path.dirname(result['output_file'])}/")

    print(f"\n📋 Usage notes:")
    print(f"   • This comprehensive file contains aggregated data from ALL weekly periods")
    print(f"   • Compatible with process_monthly_tract_flows.py for analysis")
    print(f"   • Each placekey represents cumulative activity across all weeks")
    print(f"   • 'weeks_active_count' shows how many weeks each placekey was active")
    print(f"   • 'first_seen_date' shows when each placekey first appeared in the data")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n🎉 Comprehensive weekly aggregation pipeline completed successfully!")
    else:
        print(f"\n❌ Pipeline failed!")
        sys.exit(1)
